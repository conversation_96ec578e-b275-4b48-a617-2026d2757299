// src/index.js
import React from 'react';
import ReactDOM from 'react-dom';

// 从 antd-mobile 中导入 Button 和 Toast
// Webpack 会根据 externals 配置，把这些 import 转换成对全局变量的引用
import { Button, Toast } from 'antd-mobile';

// 引入 antd-mobile 的 CSS。CSS 通常需要单独处理。
// 我们将在 HTML 中通过 CDN 引入。
// 在这里 import 'antd-mobile/dist/antd-mobile.css'; 也可以，但 CDN 更符合外部化策略。

const App = () => {
  const showToast = () => {
    Toast.show({
      content: 'Hello World, This is a long text',
      afterClose: () => {
        console.log('after')
      },
    });
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Webpack Externals antd-mobile Demo</h1>
      <p>This button is an `antd-mobile` component.</p>
      <Button type="primary" fill='solid' onClick={showToast}>
        Click Me to Show Toast
      </Button>
      <p>If this works, `antd-mobile` was loaded externally via CDN.</p>
    </div>
  );
};

ReactDOM.render(<App />, document.getElementById('root'));

// ✨ 通过全局变量来初始化 op-ui
// 确保 op-ui.js 脚本已经加载
if (window.OpUi && typeof window.OpUi.init === 'function') {
  window.OpUi.init({
    buttonText: '帮助 (独立加载)',
    modalTitle: '独立懒加载的帮助弹窗',
    modalContent: '这个弹窗由一个独立的 op-ui 库提供!',
  });
} else {
  console.error("Op-UI library is not loaded!");
}