// webpack.config.js
const path = require('path');

module.exports = {
  mode: 'production', // 使用生产模式，可以更好地观察打包大小
  entry: './src/index.js',
  output: {
    filename: 'bundle.js',
    path: path.resolve(__dirname, 'dist'),
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'],
          },
        },
      },
    ],
  },
  // ✨ 核心配置: externals ✨
  externals: {
    // key: 是你在代码中 'import ... from' 的包名
    // value: 是这个包在浏览器全局环境(window)下暴露的变量名

    // 当 Webpack 遇到 import React from 'react' 时，
    // 它不会打包 react，而是生成代码 var React = window.React;
    'react': 'React',
    
    // 同理，对应 import ReactDOM from 'react-dom'
    'react-dom': 'ReactDOM',
    
    // value 'antdMobile' 是 antd-mobile v2 在 window 上暴露的真实全局变量名
    'antd-mobile': 'antdMobile',
  },
  devServer: {
    // 告诉 dev-server 从哪里提供文件
    contentBase: [
      path.resolve(__dirname, 'dist'), // 优先服务业务工程自己的 dist
      // ✨ 同时服务 op-ui 工程的 dist 目录
      // 注意：这里的路径需要根据你的实际目录结构调整
      path.resolve(__dirname, '../op-ui-demo/dist') 
    ],
    compress: true,
    port: 9000
  }
};