/**
 * 外部依赖库加载工具
 *
 * 用于管理和初始化通过 CDN 或远程服务器加载的外部 UI 组件库
 * 包括 antd-mobile 和 op-ui 等组件库的动态加载和初始化
 *
 * 设计原则：
 * - 确保外部库在使用前已正确加载
 * - 提供统一的初始化接口
 * - 支持错误处理和降级方案
 * - 类型安全的全局变量访问
 */

// Op-UI 配置接口
interface OpUiConfig {
  buttonText?: string;
  modalTitle?: string;
  modalContent?: string;
  [key: string]: any;
}

// 声明全局变量类型
declare global {
  interface Window {
    antdMobile?: any;
    OpUi?: {
      init: (config: OpUiConfig) => void;
      [key: string]: any;
    };
  }
}

// 外部库加载状态
interface ExternalLibStatus {
  antdMobile: boolean;
  opUi: boolean;
}

/**
 * 检查外部库是否已加载
 */
export function checkExternalLibsStatus(): ExternalLibStatus {
  return {
    antdMobile: typeof window !== 'undefined' && !!window.antdMobile,
    opUi: typeof window !== 'undefined' && !!window.OpUi,
  };
}

/**
 * 等待外部库加载完成
 * @param timeout 超时时间（毫秒）
 * @returns Promise<boolean> 是否加载成功
 */
export function waitForExternalLibs(timeout = 10000): Promise<boolean> {
  return new Promise((resolve) => {
    const startTime = Date.now();

    const checkLibs = () => {
      const status = checkExternalLibsStatus();

      // 检查是否所有必需的库都已加载
      if (status.antdMobile) {
        resolve(true);
        return;
      }

      // 检查是否超时
      if (Date.now() - startTime > timeout) {
        console.warn('外部库加载超时');
        resolve(false);
        return;
      }

      // 继续等待
      setTimeout(checkLibs, 100);
    };

    checkLibs();
  });
}

/**
 * 获取 antd-mobile 组件
 * @returns antd-mobile 组件对象或 null
 */
export function getAntdMobile() {
  if (typeof window === 'undefined' || !window.antdMobile) {
    console.error('antd-mobile 未加载或不可用');
    return null;
  }
  return window.antdMobile;
}

/**
 * 初始化 Op-UI 组件库
 * @param config Op-UI 配置
 * @returns 是否初始化成功
 */
export function initOpUi(config: OpUiConfig = {}): boolean {
  if (typeof window === 'undefined' || !window.OpUi) {
    console.error('Op-UI 库未加载或不可用');
    return false;
  }

  try {
    if (typeof window.OpUi.init === 'function') {
      window.OpUi.init({
        buttonText: '帮助',
        modalTitle: '帮助信息',
        modalContent: '这是一个外部加载的帮助组件',
        ...config,
      });
      console.log('Op-UI 初始化成功');
      return true;
    } else {
      console.error('Op-UI.init 方法不存在');
      return false;
    }
  } catch (error) {
    console.error('Op-UI 初始化失败:', error);
    return false;
  }
}

/**
 * 获取 Op-UI 实例
 * @returns Op-UI 实例或 null
 */
export function getOpUi() {
  if (typeof window === 'undefined' || !window.OpUi) {
    console.error('Op-UI 未加载或不可用');
    return null;
  }
  return window.OpUi;
}

/**
 * 初始化所有外部依赖库
 * @param opUiConfig Op-UI 配置
 * @returns Promise<boolean> 是否初始化成功
 */
export async function initExternalLibs(opUiConfig: OpUiConfig = {}): Promise<boolean> {
  console.log('开始初始化外部依赖库...');

  // 等待外部库加载
  const loaded = await waitForExternalLibs();

  if (!loaded) {
    console.error('外部库加载失败');
    return false;
  }

  // 检查加载状态
  const status = checkExternalLibsStatus();
  console.log('外部库加载状态:', status);

  // 初始化 Op-UI（如果可用）
  if (status.opUi) {
    initOpUi(opUiConfig);
  }

  return true;
}

/**
 * 创建 antd-mobile 组件的安全包装器
 * 确保组件在使用前已正确加载
 */
export function createAntdMobileWrapper() {
  const antdMobile = getAntdMobile();

  if (!antdMobile) {
    // 返回一个降级的组件集合
    return {
      Button: (props: any) => {
        console.warn('antd-mobile Button 不可用，使用降级版本');
        // 返回一个简单的按钮配置对象，由调用方处理渲染
        return {
          type: 'fallback-button',
          props: {
            ...props,
            style: {
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
            },
          },
        };
      },
      Toast: {
        show: (config: any) => {
          console.warn('antd-mobile Toast 不可用，使用降级版本');
          alert(config.content || '提示信息');
        },
      },
    };
  }

  return antdMobile;
}

// 导出类型定义
export type { OpUiConfig, ExternalLibStatus };
