/**
 * 外部依赖库演示页面样式
 */

.external-libs-demo {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;

    .page-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .page-subtitle {
      font-size: 14px;
      opacity: 0.9;
    }
  }

  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1890ff;
  }

  .status-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .status-list {
      margin-bottom: 15px;
    }

    .lib-status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .lib-name {
        font-weight: 500;
        color: #333;
      }

      .status-indicator {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;

        &.loaded {
          background-color: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.not-loaded {
          background-color: #fff2f0;
          color: #ff4d4f;
          border: 1px solid #ffccc7;
        }
      }
    }

    .init-message {
      color: #666;
      font-style: italic;
      text-align: center;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
  }

  .demo-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .demo-content {
      .demo-button {
        display: block;
        width: 100%;
        padding: 12px 24px;
        margin-bottom: 15px;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:not(:disabled) {
          background-color: #1890ff;
          color: white;

          &:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }

        &:disabled {
          background-color: #f5f5f5;
          color: #bfbfbf;
          cursor: not-allowed;
        }

        &.op-ui-button {
          background-color: #722ed1;

          &:hover:not(:disabled) {
            background-color: #9254de;
          }
        }
      }

      .demo-description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        text-align: center;
      }
    }
  }

  .info-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .info-content {
      .info-item {
        display: block;
        color: #555;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 8px;
        padding-left: 10px;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          background-color: #1890ff;
          border-radius: 50%;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .external-libs-demo {
    padding: 15px;

    .header {
      padding: 15px;

      .page-title {
        font-size: 20px;
      }

      .page-subtitle {
        font-size: 12px;
      }
    }

    .demo-section,
    .status-section,
    .info-section {
      padding: 15px;
    }

    .section-title {
      font-size: 16px;
    }
  }
}
