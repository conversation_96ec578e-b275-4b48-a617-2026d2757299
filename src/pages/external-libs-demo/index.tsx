import { Component } from '@tarojs/taro';
import { MUView, MUText } from '@mu/zui';
import {
  initExternalLibs,
  createAntdMobileWrapper,
  checkExternalLibsStatus,
  getOpUi,
  type OpUiConfig
} from '../../utils/external-libs';

import './index.scss';

/**
 * 外部依赖库演示页面
 * 
 * 演示如何在 Taro 项目中使用通过 webpack externals 配置的外部依赖库
 * 包括 antd-mobile 和 op-ui 组件的使用示例
 * 
 * 功能特性：
 * - 动态检测外部库加载状态
 * - 安全的组件使用方式
 * - 错误处理和降级方案
 * - 外部库初始化演示
 */

interface State {
  libsLoaded: boolean;
  libsStatus: {
    antdMobile: boolean;
    opUi: boolean;
  };
  initMessage: string;
}

class ExternalLibsDemo extends Component<{}, State> {
  
  constructor(props: {}) {
    super(props);
    this.state = {
      libsLoaded: false,
      libsStatus: {
        antdMobile: false,
        opUi: false
      },
      initMessage: '正在检查外部库状态...'
    };
  }

  async componentDidMount() {
    await this.initializeExternalLibs();
  }

  /**
   * 初始化外部依赖库
   */
  async initializeExternalLibs() {
    try {
      // 检查当前状态
      const status = checkExternalLibsStatus();
      this.setState({ 
        libsStatus: status,
        initMessage: '正在初始化外部库...'
      });

      // Op-UI 配置
      const opUiConfig: OpUiConfig = {
        buttonText: '外部加载帮助',
        modalTitle: 'Taro + 外部依赖演示',
        modalContent: '这个帮助弹窗由外部加载的 op-ui 库提供！'
      };

      // 初始化外部库
      const success = await initExternalLibs(opUiConfig);
      
      if (success) {
        this.setState({
          libsLoaded: true,
          libsStatus: checkExternalLibsStatus(),
          initMessage: '外部库初始化成功！'
        });
      } else {
        this.setState({
          initMessage: '外部库初始化失败，将使用降级方案'
        });
      }
    } catch (error) {
      console.error('初始化外部库时出错:', error);
      this.setState({
        initMessage: '初始化过程中出现错误'
      });
    }
  }

  /**
   * 测试 antd-mobile Button 组件
   */
  handleAntdButtonClick = () => {
    const antdMobile = createAntdMobileWrapper();
    
    if (antdMobile && antdMobile.Toast) {
      antdMobile.Toast.show({
        content: '这是来自外部加载的 antd-mobile Toast！',
        afterClose: () => {
          console.log('Toast 关闭');
        }
      });
    }
  }

  /**
   * 测试 Op-UI 功能
   */
  handleOpUiTest = () => {
    const opUi = getOpUi();
    
    if (opUi && typeof opUi.showModal === 'function') {
      opUi.showModal();
    } else {
      console.log('Op-UI showModal 方法不可用');
      // 可以在这里添加降级处理
    }
  }

  /**
   * 渲染库状态指示器
   */
  renderLibStatus(libName: string, isLoaded: boolean) {
    return (
      <MUView className="lib-status-item">
        <MUText className="lib-name">{libName}:</MUText>
        <MUView className={`status-indicator ${isLoaded ? 'loaded' : 'not-loaded'}`}>
          {isLoaded ? '✅ 已加载' : '❌ 未加载'}
        </MUView>
      </MUView>
    );
  }

  /**
   * 渲染 antd-mobile 组件示例
   */
  renderAntdMobileDemo() {
    const antdMobile = createAntdMobileWrapper();
    const { Button } = antdMobile;

    return (
      <MUView className="demo-section">
        <MUText className="section-title">antd-mobile 组件演示</MUText>
        <MUView className="demo-content">
          <Button 
            type="primary" 
            fill="solid"
            onClick={this.handleAntdButtonClick}
            className="demo-button"
          >
            点击测试 antd-mobile Toast
          </Button>
          <MUText className="demo-description">
            这个按钮使用了外部加载的 antd-mobile 组件
          </MUText>
        </MUView>
      </MUView>
    );
  }

  /**
   * 渲染 Op-UI 组件示例
   */
  renderOpUiDemo() {
    const { libsStatus } = this.state;

    return (
      <MUView className="demo-section">
        <MUText className="section-title">Op-UI 组件演示</MUText>
        <MUView className="demo-content">
          <button 
            className="demo-button op-ui-button"
            onClick={this.handleOpUiTest}
            disabled={!libsStatus.opUi}
          >
            {libsStatus.opUi ? '测试 Op-UI 功能' : 'Op-UI 未加载'}
          </button>
          <MUText className="demo-description">
            这个功能依赖外部加载的 op-ui 组件库
          </MUText>
        </MUView>
      </MUView>
    );
  }

  render() {
    const { libsStatus, initMessage } = this.state;

    return (
      <MUView className="external-libs-demo">
        <MUView className="header">
          <MUText className="page-title">外部依赖库演示</MUText>
          <MUText className="page-subtitle">
            演示 Taro + webpack externals + 远程组件库
          </MUText>
        </MUView>

        {/* 库状态显示 */}
        <MUView className="status-section">
          <MUText className="section-title">外部库加载状态</MUText>
          <MUView className="status-list">
            {this.renderLibStatus('antd-mobile', libsStatus.antdMobile)}
            {this.renderLibStatus('op-ui', libsStatus.opUi)}
          </MUView>
          <MUText className="init-message">{initMessage}</MUText>
        </MUView>

        {/* antd-mobile 演示 */}
        {this.renderAntdMobileDemo()}

        {/* Op-UI 演示 */}
        {this.renderOpUiDemo()}

        {/* 说明信息 */}
        <MUView className="info-section">
          <MUText className="section-title">实现说明</MUText>
          <MUView className="info-content">
            <MUText className="info-item">
              • 通过 webpack externals 配置排除了 UI 库的打包
            </MUText>
            <MUText className="info-item">
              • UI 库通过 CDN 或远程服务器动态加载
            </MUText>
            <MUText className="info-item">
              • 提供了安全的组件包装器和降级方案
            </MUText>
            <MUText className="info-item">
              • 支持运行时检测和错误处理
            </MUText>
          </MUView>
        </MUView>
      </MUView>
    );
  }
}

export default ExternalLibsDemo;
