import { Component } from '@tarojs/taro';
import Madp from '@mu/madp';
import { MUView, MUButton, MUImage } from '@mu/zui';
import { track } from '@mu/madp-track';
import {
  observer,
} from '@tarojs/mobx';
import HomepageStore from './store';
import PersonalSetting from './components/personal-setting';

// 背景图片常量
const BACKGROUND_IMG = 'https://file.mucfc.com/cop/0/0/202403/202403220918055a77ce.png';

/**
 * 首页组件 - DDD 视图层
 *
 * 视图层是唯一与前端框架耦合的一层，负责：
 * 1. 展示用户界面
 * 2. 处理用户交互
 * 3. 调用控制层 Store 获取数据
 *
 * 设计原则：
 * - 薄视图层：不包含业务逻辑，只负责展示和交互
 * - 数据直用：从 Store 获取的数据直接用于展示，不进行转换
 * - 响应式：通过 @observer 装饰器实现响应式更新
 * - 组件化：将复杂的 UI 拆分为独立的组件
 *
 * 功能说明：
 * - 展示用户个人信息
 * - 提供页面导航功能
 * - 集成埋点追踪
 */
@track({}, {
  pageId: 'MobxTemplate',
  dispatchOnMount: true,
})
@observer
class HomePage extends Component {
  /**
   * 组件挂载前的生命周期方法
   *
   * 在组件挂载前初始化页面数据。
   * 调用控制层 Store 的 init 方法获取用户信息。
   */
  componentWillMount() {
    HomepageStore.init();
  }

  /**
   * 渲染方法
   *
   * 负责渲染页面 UI，包括：
   * 1. 背景图片展示
   * 2. 用户个人信息组件
   * 3. 页面导航按钮
   *
   * 数据来源：直接从 HomepageStore 获取，无需进行任何转换
   */
  render() {
    // 从控制层获取用户数据
    const { user } = HomepageStore;

    return (
      <MUView style={{ backgroundColor: '#f3f3f3' }}>
        {/* 背景区域 */}
        <MUView style={{ backgroundColor: '#f3f3f3', height: '320px', width: '100%', position: 'relative' }}>
          {/* 背景图片 */}
          <MUView className="demo_backgroundImage">
            <MUImage
              className="image"
              src={BACKGROUND_IMG}
              mode="widthFix"
            />
          </MUView>
          {/* 用户个人信息组件 */}
          <PersonalSetting user={user} />
        </MUView>

        {/* 导航按钮 */}
        <MUView style={{ padding: '20px' }}>
          <MUView style={{ marginBottom: '10px' }}>
            <MUButton
              beaconId="btn1"
              type="primary"
              onClick={() => {
                Madp.navigateTo({ url: '/pages/index/index' });
              }}
            >
              跳转到本应用内的index页
            </MUButton>
          </MUView>

          <MUButton
            beaconId="btn2"
            type="secondary"
            onClick={() => {
              Madp.navigateTo({ url: '/pages/external-libs-demo/index' });
            }}
          >
            外部依赖库演示
          </MUButton>
        </MUView>
      </MUView>
    );
  }
}
export default HomePage;
