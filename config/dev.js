const path = require('path');

module.exports = {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {
  },
  weapp: {},
  h5: {
    devServer: {
      port: 3002,
      // host: '0.0.0.0',
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      // 告诉 dev-server 从哪里提供文件
      contentBase: [
        path.resolve(__dirname, 'dist'), // 优先服务业务工程自己的 dist
        // ✨ 同时服务 op-ui 工程的 dist 目录
        // 注意：这里的路径需要根据你的实际目录结构调整
        path.resolve(__dirname, '../../work/op-ui-demo/dist'),
      ],
    },
  },
};
